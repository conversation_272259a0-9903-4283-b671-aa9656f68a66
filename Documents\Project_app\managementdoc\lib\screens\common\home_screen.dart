import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/constants/app_routes.dart';
import '../../providers/auth_provider.dart';
import '../../providers/user_provider.dart';
import '../../providers/document_provider.dart';
import '../../providers/category_provider.dart';
import '../../widgets/common/app_bottom_navigation.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/file_table_widget.dart';
import '../../widgets/common/empty_state_widget.dart';
import '../../widgets/common/file_filter_widget.dart';

import '../../models/document_model.dart';
import '../../services/realtime_sync_service.dart';
import '../../services/ui_refresh_service.dart';
import '../../services/file_download_service.dart';
import '../../core/services/greeting_service.dart';
import '../../utils/download_location_helper.dart';
import '../../config/firebase_config.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  bool _dataLoaded = false;
  final TextEditingController _searchController = TextEditingController();
  Timer? _searchTimer;
  Timer? _refreshTimer;
  late GreetingSet _currentGreeting;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _updateSessionActivity();
    _searchController.addListener(_onSearchChanged);
    _startAutoRefresh();
    _generateNewGreeting();

    // Initialize real-time sync service only if enabled
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (FirebaseConfig.shouldEnableRealtimeSync) {
        RealtimeSyncService.instance.initialize(context);
        RealtimeSyncService.instance.startDocumentSync();
      } else {
        debugPrint('Real-time sync disabled in FirebaseConfig');
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _searchController.dispose();
    _searchTimer?.cancel();
    _refreshTimer?.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Refresh UI when app comes back to foreground (user returns from upload screen)
    if (state == AppLifecycleState.resumed) {
      debugPrint('🔄 App resumed - triggering UI refresh');
      UIRefreshService.refreshAllProviders(context);
    }
  }

  void _startAutoRefresh() {
    // Only start auto-refresh if enabled in config
    if (!FirebaseConfig.shouldAutoRefresh) {
      debugPrint('Auto-refresh disabled in FirebaseConfig');
      return;
    }

    // Use configurable refresh interval
    _refreshTimer = Timer.periodic(FirebaseConfig.autoRefreshInterval, (timer) {
      if (mounted) {
        _refreshData();
      }
    });
  }

  void _generateNewGreeting() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final userName = authProvider.currentUser?.fullName;
    _currentGreeting = GreetingService.instance.getSmartGreeting(userName);
  }

  Future<void> _refreshData() async {
    try {
      final documentProvider = Provider.of<DocumentProvider>(
        context,
        listen: false,
      );
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final categoryProvider = Provider.of<CategoryProvider>(
        context,
        listen: false,
      );

      await Future.wait([
        documentProvider.refreshDocuments(),
        userProvider.refreshUsers(),
        categoryProvider.refreshCategories(),
      ]);

      // Generate new greeting on refresh
      _generateNewGreeting();
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      // Silently handle refresh errors to avoid disrupting user experience
      debugPrint('Auto-refresh error: $e');
    }
  }

  void _onSearchChanged() {
    if (_searchTimer?.isActive ?? false) _searchTimer!.cancel();

    // Perform search immediately if there's at least 1 character or if clearing search
    final searchText = _searchController.text.trim();
    if (searchText.isNotEmpty || searchText.isEmpty) {
      // Use minimal delay for better performance while still preventing excessive calls
      _searchTimer = Timer(const Duration(milliseconds: 100), () {
        _performSearch();
      });
    }
  }

  int _getRecentDocumentsCount(DocumentProvider documentProvider, int days) {
    final cutoffDate = DateTime.now().subtract(Duration(days: days));
    return documentProvider.allDocuments
        .where((doc) => doc.uploadedAt.isAfter(cutoffDate))
        .length;
  }

  List<DocumentModel> _filterDocuments(List<DocumentModel> documents) {
    final searchQuery = _searchController.text.toLowerCase().trim();

    if (searchQuery.isEmpty) {
      return documents;
    }

    return documents.where((document) {
      final fileName = document.fileName.toLowerCase();
      final description = document.metadata.description.toLowerCase();
      final fileType = document.fileType.toLowerCase();
      final category = document.category.toLowerCase();
      final uploadedBy = document.uploadedBy.toLowerCase();

      return fileName.contains(searchQuery) ||
          description.contains(searchQuery) ||
          fileType.contains(searchQuery) ||
          category.contains(searchQuery) ||
          uploadedBy.contains(searchQuery);
    }).toList();
  }

  void _performSearch() {
    setState(() {
      // Trigger rebuild to apply filter
    });
  }

  // Update session activity when user is active
  void _updateSessionActivity() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.updateSessionActivity();
    });
  }

  Future<void> _loadData() async {
    if (_dataLoaded) return;

    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final documentProvider = Provider.of<DocumentProvider>(
        context,
        listen: false,
      );
      final categoryProvider = Provider.of<CategoryProvider>(
        context,
        listen: false,
      );

      await Future.wait([
        userProvider.loadUsers(),
        documentProvider.loadDocuments(),
        categoryProvider.loadCategories(),
      ]);

      _dataLoaded = true;
    } catch (e) {
      debugPrint('Error loading data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        if (authProvider.currentUser == null) {
          return const PageLoadingWidget(message: 'Memuat data pengguna...');
        }

        // Load data after user is authenticated
        if (!_dataLoaded) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _loadData();
          });
        }

        return AppScaffoldWithNavigation(
          title: 'Beranda',
          currentNavIndex: 0, // Home is index 0
          showAppBar: true, // Use standard app bar like other pages
          body: _buildDashboard(),
        );
      },
    );
  }

  void _showProfileMenu(AuthProvider authProvider) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: CircleAvatar(
                backgroundColor: AppColors.primaryLight,
                backgroundImage: authProvider.currentUser?.profileImage != null
                    ? NetworkImage(authProvider.currentUser!.profileImage!)
                    : null,
                child: authProvider.currentUser?.profileImage == null
                    ? Text(
                        authProvider.currentUser?.fullName
                                .substring(0, 1)
                                .toUpperCase() ??
                            'U',
                        style: const TextStyle(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : null,
              ),
              title: Text(authProvider.currentUser?.fullName ?? ''),
              subtitle: Text(
                authProvider.currentUser?.role.toUpperCase() ?? '',
              ),
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.person_outline),
              title: const Text(AppStrings.profile),
              onTap: () {
                Navigator.pop(context);
                Navigator.of(context).pushNamed(AppRoutes.profile);
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings_outlined),
              title: const Text(AppStrings.settings),
              onTap: () {
                Navigator.pop(context);
                Navigator.of(context).pushNamed(AppRoutes.settings);
              },
            ),
            ListTile(
              leading: const Icon(Icons.logout, color: AppColors.error),
              title: const Text(
                AppStrings.logout,
                style: TextStyle(color: AppColors.error),
              ),
              onTap: () {
                Navigator.pop(context);
                _logout();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDashboard() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return RefreshIndicator(
          onRefresh: _refreshData,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Container(
              color: AppColors.background,
              child: Column(
                children: [
                  // Greeting Section
                  _buildGreetingSection(authProvider),

                  // Dashboard Statistics Section (Admin only)
                  if (authProvider.isAdmin) _buildDashboardStats(),

                  // Search Section
                  _buildSearchSection(),

                  // File List Section
                  _buildFileListSection(),

                  // Bottom white space for better visual separation
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildGreetingSection(AuthProvider authProvider) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Profile Avatar
          GestureDetector(
            onTap: () => _showProfileMenu(authProvider),
            child: CircleAvatar(
              radius: 24,
              backgroundColor: AppColors.primaryLight,
              backgroundImage: authProvider.currentUser?.profileImage != null
                  ? NetworkImage(authProvider.currentUser!.profileImage!)
                  : null,
              child: authProvider.currentUser?.profileImage == null
                  ? Text(
                      authProvider.currentUser?.fullName
                              .substring(0, 1)
                              .toUpperCase() ??
                          'U',
                      style: const TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    )
                  : null,
            ),
          ),
          const SizedBox(width: 16),
          // Greeting Text
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _currentGreeting.personalGreeting,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _currentGreeting.mainGreeting,
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          // Profile Menu Icon
          IconButton(
            onPressed: () => _showProfileMenu(authProvider),
            icon: const Icon(
              Icons.arrow_forward_ios,
              color: AppColors.textSecondary,
              size: 16,
            ),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardStats() {
    return Consumer3<DocumentProvider, UserProvider, CategoryProvider>(
      builder:
          (context, documentProvider, userProvider, categoryProvider, child) {
            final totalDocuments = documentProvider.documents.length;
            final recentDocuments = _getRecentDocumentsCount(
              documentProvider,
              7,
            );
            final totalUsers = userProvider.users.length;
            final totalCategories = categoryProvider.categories.length;

            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'Total Files',
                      totalDocuments.toString(),
                      Icons.description,
                      AppColors.primary,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildStatCard(
                      'Recent',
                      recentDocuments.toString(),
                      Icons.access_time,
                      AppColors.success,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildStatCard(
                      'Users',
                      totalUsers.toString(),
                      Icons.people,
                      AppColors.warning,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildStatCard(
                      'Categories',
                      totalCategories.toString(),
                      Icons.folder,
                      AppColors.info,
                    ),
                  ),
                ],
              ),
            );
          },
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 10,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 2),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.searchBackground,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(8),
        ),
        child: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'Search your file',
            hintStyle: GoogleFonts.poppins(
              color: AppColors.textSecondary,
              fontSize: 14,
            ),
            prefixIcon: const Icon(
              Icons.search,
              color: AppColors.textSecondary,
            ),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(
                      Icons.clear,
                      color: AppColors.textSecondary,
                    ),
                    onPressed: () {
                      _searchController.clear();
                      setState(() {
                        // Trigger rebuild to clear filter
                      });
                    },
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            filled: true,
            fillColor: AppColors.surface,
          ),
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: AppColors.textPrimary,
          ),
        ),
      ),
    );
  }

  Widget _buildFileListSection() {
    return Container(
      // Tambahan margin khusus untuk Recent Files section
      margin: const EdgeInsets.fromLTRB(
        16,
        8,
        16,
        0,
      ), // Custom margin: left, top, right, bottom
      child: Consumer<DocumentProvider>(
        builder: (context, documentProvider, child) {
          // Apply search filter to the already filtered documents from DocumentProvider
          final searchFilteredDocuments = _filterDocuments(
            documentProvider.documents,
          );

          return FileTableWidget(
            documents: searchFilteredDocuments,
            mode: FileTableMode
                .manage, // Changed from view to manage for consistent styling
            title: 'Recent Files',
            isLoading: documentProvider.isLoading,
            showFilter: true,
            showRefresh: false,
            enableScroll:
                false, // Disable internal scroll to allow full page scroll
            bottomSpacing:
                16, // Increased bottom spacing for better scrolling experience
            onFilter: _showFilterMenu,
            onDocumentTap: (document) {
              // Handle document tap - could navigate to document details
              _showDocumentDetails(document);
            },
            onDocumentMenu: _showDocumentMenu,
            emptyStateWidget: EmptyStateWidget.noDocuments(
              showContainer: false,
              padding: const EdgeInsets.symmetric(vertical: 60, horizontal: 32),
            ),
            // Apply same custom columns as category files screen
            customColumns: [
              TableColumn(
                type: TableColumnType.fileName,
                title: 'Name',
                width: const FlexColumnWidth(7),
              ),
              TableColumn(
                type: TableColumnType.uploadDate,
                title: 'Date',
                width: const FlexColumnWidth(2),
                alignment: TextAlign.center,
              ),
              TableColumn(
                type: TableColumnType.actions,
                title: 'Action',
                width: const FixedColumnWidth(50),
                alignment: TextAlign.center,
              ),
            ],
          );
        },
      ),
    );
  }

  void _showFilterMenu() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => FileFilterWidget(
        onFilterApplied: () {
          // Trigger UI refresh if needed
          setState(() {});
        },
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60,
            child: Text(
              '$label:',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.poppins(color: AppColors.textPrimary),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _logout() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.logout();
    if (mounted) {
      Navigator.of(context).pushReplacementNamed(AppRoutes.login);
    }
  }

  void _showDeleteConfirmation(DocumentModel document) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'Delete File',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Text(
            'Are you sure you want to delete "${document.fileName}"? This action cannot be undone.',
            style: GoogleFonts.poppins(fontSize: 14, color: Colors.black54),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteFile(document);
              },
              child: Text(
                'Delete',
                style: GoogleFonts.poppins(
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteFile(DocumentModel document) async {
    try {
      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 12),
              Text('Deleting ${document.fileName}...'),
            ],
          ),
          duration: const Duration(seconds: 2),
        ),
      );

      // Get current user ID for logging
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUserId = authProvider.currentUser?.id ?? 'unknown';

      // Get document provider and remove the document permanently
      final documentProvider = Provider.of<DocumentProvider>(
        context,
        listen: false,
      );
      await documentProvider.removeDocument(document.id, currentUserId);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${document.fileName} deleted permanently from storage',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete file: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // Helper methods for document display

  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return DateFormat('dd/MM/yyyy').format(date);
    }
  }

  void _showDocumentMenu(DocumentModel document) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.download),
              title: Text('Download', style: GoogleFonts.poppins()),
              onTap: () {
                Navigator.pop(context);
                _downloadFile(document);
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: Text('Share', style: GoogleFonts.poppins()),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Sharing ${document.fileName}...')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: Text(
                'Delete',
                style: GoogleFonts.poppins(color: Colors.red),
              ),
              onTap: () {
                Navigator.pop(context);
                _showDeleteConfirmation(document);
              },
            ),

            ListTile(
              leading: const Icon(Icons.info_outline),
              title: Text('Details', style: GoogleFonts.poppins()),
              onTap: () {
                Navigator.pop(context);
                _showDocumentDetails(document);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showDocumentDetails(DocumentModel document) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Document Details',
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
        ),
        content: Consumer<UserProvider>(
          builder: (context, userProvider, child) {
            final user = userProvider.getUserById(document.uploadedBy);
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailRow('Name', document.fileName),
                _buildDetailRow('Owner', user?.fullName ?? 'Unknown'),
                _buildDetailRow('Size', _formatFileSize(document.fileSize)),
                _buildDetailRow('Type', document.fileType),
                _buildDetailRow('Uploaded', _formatDate(document.uploadedAt)),
                _buildDetailRow('Status', document.status.toUpperCase()),
                if (document.metadata.description.isNotEmpty)
                  _buildDetailRow('Description', document.metadata.description),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close', style: GoogleFonts.poppins()),
          ),
        ],
      ),
    );
  }

  // Download file to device storage
  Future<void> _downloadFile(DocumentModel document) async {
    final downloadService = FileDownloadService();

    try {
      // Show initial download message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(child: Text('Downloading ${document.fileName}...')),
              ],
            ),
            duration: const Duration(seconds: 30), // Long duration for download
            backgroundColor: AppColors.primary,
          ),
        );
      }

      // Download the file
      await downloadService.downloadFile(
        document,
        onProgress: (progress) {
          // You could update a progress indicator here if needed
          debugPrint(
            'Download progress: ${(progress * 100).toStringAsFixed(1)}%',
          );
        },
      );

      // Show success message with location info
      if (mounted) {
        final locationDescription = await downloadService
            .getDownloadLocationDescription();
        final actualPath = await downloadService.getDownloadDirectoryPath();

        if (mounted) {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: Colors.white,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'File downloaded successfully!',
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  Text(
                    'File: ${document.fileName}',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.white.withValues(alpha: 0.9),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'Location: $locationDescription',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'Path: $actualPath',
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
              backgroundColor: AppColors.success,
              duration: const Duration(seconds: 8),
              action: SnackBarAction(
                label: 'Find File',
                textColor: Colors.white,
                onPressed: () {
                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                  DownloadLocationHelper.showDownloadLocationInfo(context);
                },
              ),
            ),
          );
        }
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.error, color: Colors.white, size: 20),
                    const SizedBox(width: 8),
                    const Expanded(child: Text('Download failed')),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  e.toString(),
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
                _downloadFile(document); // Retry download
              },
            ),
          ),
        );
      }
    }
  }
}
